# src/services/bots/trading_bot.py
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from collections import deque
from core.interfaces import IStrategy, IBrokerage, IDatabase
from core.interfaces import BotStatus, MarketData
from core.exceptions import (
    TradingBotAlreadyRunningError,
    TradingBotNotRunningError,
    TradingBotAlreadyPausedError,
    TradingBotNotPausedError,
    StrategyError,
    BrokerageError
)
from core.bot import BaseBot


class TradingBot(BaseBot):
    """Main trading bot implementation"""

    def __init__(
        self,
        name: str,
        config: Dict[str, Any],
        strategy: IStrategy = None,
        brokerage: IBrokerage = None,
        database: IDatabase = None,
        logger: logging.Logger = None
    ):
        super().__init__(name, config)
        self.strategy = strategy
        self.brokerage = brokerage
        self.database = database
        self.logger = logger or logging.getLogger(f"TradingBot.{name}")
        self.mode = config.get('mode', 'test')
        self.update_interval = config.get('update_interval', 5)  # seconds
        self._shutdown_event = asyncio.Event()
        self._pause_event = asyncio.Event()
        self._logs = deque(maxlen=config.get('max_logs', 1000))
        self._last_error: Optional[Exception] = None
        self._error_count = 0
        self._max_errors = config.get('max_errors', 10)

        # Set initial pause state (not paused)
        self._pause_event.set()

    def _log(self, level: str, message: str, extra_data: Dict[str, Any] = None) -> None:
        """Internal logging method that stores logs and forwards to logger"""
        log_entry = {
            'timestamp': datetime.now(),
            'level': level,
            'message': message,
            'bot_name': self.name,
            'status': self.status.value if hasattr(self.status, 'value') else str(self.status),
            **(extra_data or {})
        }

        self._logs.append(log_entry)

        # Forward to logger
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(f"[{self.name}] {message}")

    async def initialize(self) -> None:
        """Initialize the bot"""
        self._log('info', f"Initializing bot {self.name}")
        self.status = BotStatus.STARTING

        try:
            # Validate required components
            if not self.strategy:
                raise ValueError("Strategy is required")
            if not self.brokerage:
                raise ValueError("Brokerage is required")
            if not self.database:
                raise ValueError("Database is required")

            # Initialize components
            await self.database.connect()
            await self.brokerage.connect()
            await self.strategy.initialize()

            self._log('info', f"Bot {self.name} initialized successfully")

        except Exception as e:
            self.status = BotStatus.ERROR
            self._log('error', f"Failed to initialize bot {self.name}: {e}")
            await self.handle_error(e)
            raise

    async def start(self) -> None:
        """Start the bot"""
        if self.status == BotStatus.RUNNING:
            raise TradingBotAlreadyRunningError(f"Bot {self.name} is already running")

        self._log('info', f"Starting bot {self.name}")

        try:
            # Initialize if not already done
            if self.status not in [BotStatus.PAUSED, BotStatus.STOPPED]:
                await self.initialize()

            # Reset events
            self._shutdown_event.clear()
            self._pause_event.set()

            # Create and start main task
            self._task = asyncio.create_task(self.run())
            self.status = BotStatus.RUNNING
            self._error_count = 0

            self._log('info', f"Bot {self.name} started successfully")

        except Exception as e:
            self.status = BotStatus.ERROR
            self._log('error', f"Failed to start bot {self.name}: {e}")
            await self.handle_error(e)
            raise

    async def stop(self) -> None:
        """Stop the bot"""
        if self.status not in [BotStatus.RUNNING, BotStatus.PAUSED]:
            raise TradingBotNotRunningError(f"Bot {self.name} is not running")

        self._log('info', f"Stopping bot {self.name}")
        self.status = BotStatus.STOPPING

        # Signal shutdown
        self._shutdown_event.set()
        self._pause_event.set()  # Ensure bot can exit pause state

        # Wait for task to complete gracefully
        if self._task and not self._task.done():
            try:
                await asyncio.wait_for(self._task, timeout=30.0)
            except asyncio.TimeoutError:
                self._log(
                    'warning', f"Bot {self.name} did not stop gracefully, cancelling task")
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass

        # Cleanup resources
        await self._cleanup()
        self.status = BotStatus.STOPPED
        self._task = None

        self._log('info', f"Bot {self.name} stopped")

    async def pause(self) -> None:
        """Pause the bot"""
        if self.status != BotStatus.RUNNING:
            raise TradingBotNotRunningError(f"Bot {self.name} is not running")

        if self.status == BotStatus.PAUSED:
            raise TradingBotAlreadyPausedError(f"Bot {self.name} is already paused")

        self._log('info', f"Pausing bot {self.name}")
        self.status = BotStatus.PAUSED
        self._pause_event.clear()

        self._log('info', f"Bot {self.name} paused")

    async def resume(self) -> None:
        """Resume the bot"""
        if self.status != BotStatus.PAUSED:
            raise TradingBotNotPausedError(f"Bot {self.name} is not paused")

        self._log('info', f"Resuming bot {self.name}")
        self.status = BotStatus.RUNNING
        self._pause_event.set()

        self._log('info', f"Bot {self.name} resumed")

    async def run(self) -> None:
        """Main bot execution loop"""
        self._log('info', f"Bot {self.name} entering main loop")

        try:
            while not self._shutdown_event.is_set():
                try:
                    # Wait if paused
                    await self._pause_event.wait()

                    # Check if we should shutdown after unpausing
                    if self._shutdown_event.is_set():
                        break

                    # Get symbols to monitor from strategy config
                    symbols = self.strategy.config.get('symbols', ['BTCUSDT'])

                    for symbol in symbols:
                        # Check for shutdown/pause between symbols
                        if self._shutdown_event.is_set():
                            break

                        await self._pause_event.wait()

                        # Get market data
                        market_data = await self.brokerage.get_market_data(symbol)

                        # Analyze market and get trading decision
                        order_request = await self.strategy.analyze_market(market_data)

                        if order_request:
                            self._log(
                                'info', f"Strategy suggests order: {order_request}")

                            # Execute order if in live mode
                            if self.mode == 'live':
                                try:
                                    order = await self.brokerage.place_order(order_request)
                                    self._log('info', f"Order placed: {order}")
                                    await self.strategy.on_order_filled(order)

                                    # Store order in database
                                    await self._store_order(order)

                                except Exception as e:
                                    self._log(
                                        'error', f"Failed to place order: {e}")
                                    await self.handle_error(e)
                            else:
                                self._log(
                                    'info', f"Test mode: Would place order {order_request}")

                    # Wait for next iteration (respect pause state)
                    for _ in range(self.update_interval):
                        if self._shutdown_event.is_set():
                            break
                        await asyncio.sleep(1)
                        await self._pause_event.wait()

                except Exception as e:
                    await self.handle_error(e)

        except asyncio.CancelledError:
            self._log('info', f"Bot {self.name} main loop cancelled")
        except Exception as e:
            self._log('error', f"Fatal error in bot {self.name}: {e}")
            self.status = BotStatus.ERROR
            await self.handle_error(e)
        finally:
            await self._cleanup()

    async def handle_error(self, error: Exception) -> None:
        """Handle errors during bot execution"""
        self._last_error = error
        self._error_count += 1

        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'error_count': self._error_count
        }

        self._log('error', f"Error #{self._error_count}: {error}", error_data)

        # If too many errors, stop the bot
        if self._error_count >= self._max_errors:
            self._log(
                'critical', f"Max errors ({self._max_errors}) reached, stopping bot")
            self.status = BotStatus.ERROR
            await self.stop()
        else:
            # Brief pause before retrying
            # Exponential backoff capped at 60s
            await asyncio.sleep(min(5 * self._error_count, 60))

    async def get_bot_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status"""
        task_info = None
        if self._task:
            task_info = {
                'done': self._task.done(),
                'cancelled': self._task.cancelled(),
                'exception': str(self._task.exception()) if self._task.done() and self._task.exception() else None
            }

        return {
            'name': self.name,
            'status': self.status.value if hasattr(self.status, 'value') else str(self.status),
            'mode': self.mode,
            'update_interval': self.update_interval,
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'strategy': {
                'name': self.strategy.__class__.__name__ if self.strategy else None,
                'config': self.strategy.config if self.strategy else None
            },
            'brokerage': {
                'name': self.brokerage.__class__.__name__ if self.brokerage else None,
                'connected': await self.brokerage.is_connected() if self.brokerage else False
            },
            'database': {
                'connected': await self.database.is_connected() if self.database else False
            },
            'error_info': {
                'last_error': str(self._last_error) if self._last_error else None,
                'error_count': self._error_count,
                'max_errors': self._max_errors
            },
            'task_info': task_info,
            'log_count': len(self._logs)
        }

    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update bot configuration"""
        old_config = self.config.copy()

        try:
            # Update configuration
            self.config.update(new_config)

            # Update derived properties
            self.mode = self.config.get('mode', 'test')
            self.update_interval = self.config.get('update_interval', 5)
            self._max_errors = self.config.get('max_errors', 10)

            # Resize log deque if max_logs changed
            max_logs = self.config.get('max_logs', 1000)
            if max_logs != old_config.get('max_logs', 1000):
                new_logs = deque(self._logs, maxlen=max_logs)
                self._logs = new_logs

            self._log('info', f"Configuration updated",
                      {'config_changes': new_config})

            # Update strategy config if provided
            if 'strategy' in new_config and self.strategy:
                await self.strategy.update_config(new_config['strategy'])

        except Exception as e:
            # Rollback configuration on error
            self.config = old_config
            self._log('error', f"Failed to update configuration: {e}")
            raise

    async def get_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get bot logs"""
        # Return the most recent logs up to the limit
        logs = list(self._logs)

        # Convert datetime objects to strings for JSON serialization
        serialized_logs = []
        for log in logs[-limit:]:
            serialized_log = log.copy()
            if isinstance(serialized_log.get('timestamp'), datetime):
                serialized_log['timestamp'] = serialized_log['timestamp'].isoformat()
            serialized_logs.append(serialized_log)

        return serialized_logs

    def set_strategy(self, strategy: IStrategy) -> None:
        """Set the trading strategy"""
        if self.is_running:
            raise RuntimeError("Cannot change strategy while bot is running")

        self.strategy = strategy
        self._log('info', f"Strategy set to {strategy.__class__.__name__}")

    def set_brokerage(self, brokerage: IBrokerage) -> None:
        """Set the brokerage"""
        if self.is_running:
            raise RuntimeError("Cannot change brokerage while bot is running")

        self.brokerage = brokerage
        self._log('info', f"Brokerage set to {brokerage.__class__.__name__}")

    # Keep existing methods
    async def _store_order(self, order) -> None:
        """Store order in database"""
        try:
            query = """
                INSERT INTO orders (id, symbol, side, order_type, quantity, price, status, created_at)
                VALUES (:id, :symbol, :side, :order_type, :quantity, :price, :status, :created_at)
            """
            params = {
                'id': order.id,
                'symbol': order.symbol,
                'side': order.side.value,
                'order_type': order.order_type.value,
                'quantity': order.quantity,
                'price': order.price,
                'status': order.status,
                'created_at': order.created_at or datetime.now()
            }
            await self.database.execute(query, params)
            self._log('debug', f"Order stored in database: {order.id}")

        except Exception as e:
            self._log('error', f"Failed to store order: {e}")
            raise

    async def _cleanup(self) -> None:
        """Cleanup resources"""
        self._log('info', f"Starting cleanup for bot {self.name}")

        cleanup_errors = []

        try:
            if self.strategy:
                await self.strategy.shutdown()
        except Exception as e:
            cleanup_errors.append(f"Strategy cleanup failed: {e}")

        try:
            if self.brokerage:
                await self.brokerage.disconnect()
        except Exception as e:
            cleanup_errors.append(f"Brokerage cleanup failed: {e}")

        try:
            if self.database:
                await self.database.disconnect()
        except Exception as e:
            cleanup_errors.append(f"Database cleanup failed: {e}")

        if cleanup_errors:
            error_msg = "; ".join(cleanup_errors)
            self._log('error', f"Cleanup errors: {error_msg}")
        else:
            self._log(
                'info', f"Cleanup completed successfully for bot {self.name}")

    # Legacy method for backward compatibility
    def get_status(self) -> Dict[str, Any]:
        """Get basic bot status information (legacy method)"""
        return {
            'name': self.name,
            'status': self.status.value if hasattr(self.status, 'value') else str(self.status),
            'mode': self.mode,
            'strategy': self.strategy.__class__.__name__ if self.strategy else None,
            'brokerage': self.brokerage.__class__.__name__ if self.brokerage else None,
            'is_running': self.is_running
        }
