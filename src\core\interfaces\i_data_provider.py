from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional, List, Callable
from .common import MarketData


class IDataProvider(ABC):
    """Abstract base class for market data providers"""

    @abstractmethod
    async def get_historical_data(self, symbol: str, interval: str, start_time: datetime,
                                  end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get historical market data"""
        pass

    @abstractmethod
    async def get_real_time_data(self, symbol: str) -> MarketData:
        """Get real-time market data"""
        pass

    @abstractmethod
    async def subscribe_to_data_feed(self, symbol: str, callback: Callable) -> None:
        """Subscribe to real-time data feed"""
        pass
