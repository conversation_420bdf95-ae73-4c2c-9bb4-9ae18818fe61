# src/services/logging/factory.py
import logging
import logging.handlers
import os
from typing import Dict, Any
from core.exceptions import ConfigurationError


class LoggingFactory:
    """Factory for creating and configuring loggers"""
    
    @staticmethod
    def create_logger(name: str, config: Dict[str, Any]) -> logging.Logger:
        """Create and configure a logger"""
        logger = logging.getLogger(name)
        
        # Clear any existing handlers
        logger.handlers.clear()
        
        # Set level
        level = config.get('level', 'INFO')
        logger.setLevel(getattr(logging, level))
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler
        log_file = config.get('file')
        if log_file:
            try:
                # Ensure directory exists
                log_dir = os.path.dirname(log_file)
                if log_dir:
                    os.makedirs(log_dir, exist_ok=True)
                
                # Create rotating file handler
                file_handler = logging.handlers.RotatingFileHandler(
                    log_file,
                    maxBytes=10*1024*1024,  # 10MB
                    backupCount=5
                )
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
            except Exception as e:
                raise ConfigurationError(f"Failed to create log file {log_file}: {e}")
        
        # Prevent propagation to root logger
        logger.propagate = False
        
        return logger