from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import asyncio
from .common import BotStatus
from .i_strategy import IStrategy
from .i_brokerage import IBrokerage

class IBot(ABC):
    """Abstract base class for trading bots"""

    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.status = BotStatus.STOPPED
        self._task: Optional[asyncio.Task] = None
        self.strategy: Optional[IStrategy] = None
        self.brokerage: Optional[IBrokerage] = None

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the bot"""
        pass

    @abstractmethod
    async def start(self) -> None:
        """Start the bot"""
        pass

    @abstractmethod
    async def stop(self) -> None:
        """Stop the bot"""
        pass

    @abstractmethod
    async def pause(self) -> None:
        """Pause the bot"""
        pass

    @abstractmethod
    async def resume(self) -> None:
        """Resume the bot"""
        pass

    @abstractmethod
    async def run(self) -> None:
        """Main bot execution loop"""
        pass

    @abstractmethod
    async def handle_error(self, error: Exception) -> None:
        """Handle errors during bot execution"""
        pass

    @abstractmethod
    async def get_bot_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status"""
        pass

    @abstractmethod
    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update bot configuration"""
        pass

    @abstractmethod
    async def get_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get bot logs"""
        pass

    @abstractmethod
    def set_strategy(self, strategy: IStrategy) -> None:
        """Set the trading strategy"""
        pass

    @abstractmethod
    def set_brokerage(self, brokerage: IBrokerage) -> None:
        """Set the brokerage"""
        pass

    @property
    def is_running(self) -> bool:
        return self.status == BotStatus.RUNNING

    @property
    def is_paused(self) -> bool:
        return self.status == BotStatus.PAUSED

    @property
    def task(self) -> Optional[asyncio.Task]:
        return self._task