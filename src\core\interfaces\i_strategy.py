from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
from .common import MarketData, OrderRequest, Order, Trade
from .i_brokerage import IBrokerage


class IStrategy(ABC):
    """Abstract base class for trading strategies"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_running = False
        self.brokerage: Optional['IBrokerage'] = None

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the strategy"""
        pass

    @abstractmethod
    async def analyze_market(self, market_data: MarketData) -> Optional[OrderRequest]:
        """Analyze market data and return order request if action needed"""
        pass

    @abstractmethod
    async def on_order_filled(self, order: Order) -> None:
        """Handle order fill events"""
        pass

    @abstractmethod
    async def on_order_cancelled(self, order: Order) -> None:
        """Handle order cancellation events"""
        pass

    @abstractmethod
    async def on_order_rejected(self, order: Order) -> None:
        """Handle order rejection events"""
        pass

    @abstractmethod
    async def on_trade_executed(self, trade: Trade) -> None:
        """Handle trade execution events"""
        pass

    @abstractmethod
    async def shutdown(self) -> None:
        """Shutdown the strategy"""
        pass

    @abstractmethod
    def set_brokerage(self, brokerage: 'IBrokerage') -> None:
        """Set the brokerage instance"""
        pass

    @abstractmethod
    async def get_strategy_status(self) -> Dict[str, Any]:
        """Get current strategy status and metrics"""
        pass

    @abstractmethod
    async def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update strategy configuration"""
        pass

    @abstractmethod
    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get current positions managed by the strategy"""
        pass

    @abstractmethod
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        pass