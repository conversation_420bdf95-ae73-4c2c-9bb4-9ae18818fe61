# src/services/strategies/base_strategy.py
import asyncio
import logging
from typing import Dict, Any, Optional
from core.interfaces import IStrategy, MarketData, OrderRequest, Order


class BaseStrategy(IStrategy):
    """Base implementation for trading strategies"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = logging.getLogger(f"strategy.{self.__class__.__name__}")
        self.symbols = config.get('symbols', [])
        self.risk_management = config.get('risk_management', {})

    async def analyze_market(self, market_data: MarketData) -> Optional[OrderRequest]:
        """Analyze market data and return order request if action needed"""
        pass

    async def on_order_filled(self, order: Order) -> None:
        """Handle order fill events"""
        pass

    async def on_order_cancelled(self, order: Order) -> None:
        """Handle order cancellation events"""
        pass

    async def initialize(self) -> None:
        """Initialize the strategy"""
        self.logger.info(f"Initializing {self.__class__.__name__}")
        self.is_running = True

    async def shutdown(self) -> None:
        """Shutdown the strategy"""
        self.logger.info(f"Shutting down {self.__class__.__name__}")
        self.is_running = False

    def _calculate_position_size(self, symbol: str, price: float) -> float:
        """Calculate position size based on risk management rules"""
        # Basic position sizing - can be overridden by subclasses
        max_position_size = self.risk_management.get(
            'max_position_size', 1000.0)
        risk_per_trade = self.risk_management.get('risk_per_trade', 0.02)

        # Simple position sizing logic
        return min(max_position_size, (risk_per_trade * 10000) / price)
