from manager.bot_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from manager.config_loader import config

# Global instances
_bot_manager: BotManager = None

async def get_bot_manager() -> BotManager:
    """Dependency to get the bot manager instance"""
    global _bot_manager
    
    if _bot_manager is None:
        _bot_manager = BotManager(config)
        await _bot_manager.initialize()
    
    return _bot_manager
