# src/services/brokerages/base_brokerage.py
import asyncio
import logging
from typing import Dict, Any, List, Optional
from core.interfaces import IBrokerage, Balance, Ticker, MarketData, Order, OrderRequest
from core.exceptions import BrokerageError


class BaseBrokerage(IBrokerage):
    """Base implementation for brokerage connections"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = logging.getLogger(f"brokerage.{self.__class__.__name__}")
        self.api_key = config.get('api_key', '')
        self.api_secret = config.get('api_secret', '')
        self.sandbox = config.get('sandbox', True)
    
    async def connect(self) -> None:
        """Connect to the brokerage API"""
        self.logger.info(f"Connecting to {self.__class__.__name__}")
        # Validate credentials
        if not self.api_key or not self.api_secret:
            raise BrokerageError("API credentials not provided")
        self.is_connected = True
    
    async def disconnect(self) -> None:
        """Disconnect from the brokerage API"""
        self.logger.info(f"Disconnecting from {self.__class__.__name__}")
        self.is_connected = False
    
    def _validate_connection(self) -> None:
        """Validate connection status"""
        if not self.is_connected:
            raise BrokerageError("Not connected to brokerage")