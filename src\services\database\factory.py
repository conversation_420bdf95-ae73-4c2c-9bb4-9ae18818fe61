# src/services/database/factory.py
from sqlalchemy import text
from typing import Dict, Any
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from core.interfaces import IDatabase
from core.exceptions import DatabaseError, ConfigurationError


Base = declarative_base()


class AsyncDatabase(IDatabase):
    """Async database implementation using SQLAlchemy"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.engine = None
        self.session_factory = None
        self._connection_string = self._build_connection_string()
    
    def _build_connection_string(self) -> str:
        """Build database connection string"""
        engine = self.config.get('engine', 'sqlite')
        
        if engine == 'sqlite':
            db_name = self.config.get('database', 'bot.db')
            return f"sqlite+aiosqlite:///{db_name}"
        elif engine == 'postgres':
            user = self.config.get('user', '')
            password = self.config.get('password', '')
            host = self.config.get('host', 'localhost')
            port = self.config.get('port', '5432')
            database = self.config.get('database', '')
            return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{database}"
        elif engine == 'mysql':
            user = self.config.get('user', '')
            password = self.config.get('password', '')
            host = self.config.get('host', 'localhost')
            port = self.config.get('port', '3306')
            database = self.config.get('database', '')
            return f"mysql+aiomysql://{user}:{password}@{host}:{port}/{database}"
        else:
            raise ConfigurationError(f"Unsupported database engine: {engine}")
    
    async def connect(self) -> None:
        """Connect to the database"""
        try:
            self.engine = create_async_engine(
                self._connection_string,
                echo=False,
                pool_pre_ping=True
            )
            self.session_factory = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Test connection
            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
                
        except Exception as e:
            raise DatabaseError(f"Failed to connect to database: {e}")
    
    async def disconnect(self) -> None:
        """Disconnect from the database"""
        if self.engine:
            await self.engine.dispose()
    
    async def execute(self, query: str, params: Dict[str, Any] = None) -> Any:
        """Execute a query"""
        if not self.session_factory:
            raise DatabaseError("Database not connected")
        
        try:
            async with self.session_factory() as session:
                result = await session.execute(query, params or {})
                await session.commit()
                return result
        except Exception as e:
            raise DatabaseError(f"Failed to execute query: {e}")
    
    async def fetch_one(self, query: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Fetch one record"""
        if not self.session_factory:
            raise DatabaseError("Database not connected")
        
        try:
            async with self.session_factory() as session:
                result = await session.execute(query, params or {})
                row = result.fetchone()
                return dict(row._mapping) if row else None
        except Exception as e:
            raise DatabaseError(f"Failed to fetch record: {e}")
    
    async def fetch_all(self, query: str, params: Dict[str, Any] = None) -> list:
        """Fetch all records"""
        if not self.session_factory:
            raise DatabaseError("Database not connected")
        
        try:
            async with self.session_factory() as session:
                result = await session.execute(query, params or {})
                rows = result.fetchall()
                return [dict(row._mapping) for row in rows]
        except Exception as e:
            raise DatabaseError(f"Failed to fetch records: {e}")


class DatabaseFactory:
    """Factory for creating database instances"""
    
    @staticmethod
    def create_database(config: Dict[str, Any]) -> IDatabase:
        """Create database instance based on configuration"""
        return AsyncDatabase(config)
