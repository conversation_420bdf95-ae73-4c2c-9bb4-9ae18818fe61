from abc import ABC, abstractmethod
from .common import MarketData, Order
from .common import Trade


class IEventHandler(ABC):
    """Abstract base class for event handlers"""

    @abstractmethod
    async def handle_market_data(self, market_data: MarketData) -> None:
        """Handle market data events"""
        pass

    @abstractmethod
    async def handle_order_update(self, order: Order) -> None:
        """Handle order update events"""
        pass

    @abstractmethod
    async def handle_trade_execution(self, trade: Trade) -> None:
        """Handle trade execution events"""
        pass