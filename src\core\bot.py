"""
Core bot module providing base bot functionality.
This module serves as a bridge between the abstract IBot interface
and concrete bot implementations.
"""

from abc import ABC
from typing import Dict, Any, Optional
import asyncio
from datetime import datetime

from .interfaces import IBot, BotStatus


class BaseBot(IBot, ABC):
    """
    Base implementation of IBot providing common functionality
    for all trading bots.
    """

    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self._status = BotStatus.STOPPED
        self._task: Optional[asyncio.Task] = None
        self._stop_event = asyncio.Event()
        self._start_time: Optional[datetime] = None
        self._error_count = 0
        self._last_activity: Optional[datetime] = None

    @property
    def is_running(self) -> bool:
        """Check if the bot is currently running."""
        return self._status == BotStatus.RUNNING and self._task is not None and not self._task.done()

    @property
    def task(self) -> Optional[asyncio.Task]:
        """Get the asyncio task associated with the bot."""
        return self._task

    @property
    def status(self) -> BotStatus:
        """Get the current bot status."""
        return self._status

    @property
    def uptime(self) -> Optional[float]:
        """Get bot uptime in seconds."""
        if self._start_time and self._status == BotStatus.RUNNING:
            return (datetime.utcnow() - self._start_time).total_seconds()
        return None

    @property
    def error_count(self) -> int:
        """Get the number of errors encountered."""
        return self._error_count

    @property
    def last_activity(self) -> Optional[datetime]:
        """Get timestamp of last activity."""
        return self._last_activity

    def _update_activity(self):
        """Update the last activity timestamp."""
        self._last_activity = datetime.utcnow()

    def _increment_error_count(self):
        """Increment the error counter."""
        self._error_count += 1

    async def start(self) -> None:
        """Start the bot operation."""
        if self.is_running:
            raise RuntimeError(f"Bot {self.name} is already running")

        self._status = BotStatus.STARTING
        self._stop_event.clear()

        try:
            await self.initialize()
            self._task = asyncio.create_task(self._run_with_error_handling())
            self._start_time = datetime.utcnow()
            self._status = BotStatus.RUNNING
            self._update_activity()
        except Exception as e:
            self._status = BotStatus.ERROR
            self._increment_error_count()
            raise e

    async def stop(self) -> None:
        """Stop the bot operation."""
        if not self.is_running:
            return

        self._status = BotStatus.STOPPING
        self._stop_event.set()

        if self._task:
            try:
                await asyncio.wait_for(self._task, timeout=30.0)
            except asyncio.TimeoutError:
                self._task.cancel()
                try:
                    await self._task
                except asyncio.CancelledError:
                    pass

        self._status = BotStatus.STOPPED
        self._task = None
        self._start_time = None

    async def _run_with_error_handling(self) -> None:
        """Run the bot with error handling and recovery."""
        try:
            await self.run()
        except asyncio.CancelledError:
            # Normal shutdown
            pass
        except Exception as e:
            self._status = BotStatus.ERROR
            self._increment_error_count()
            # Log error here if logger is available
            raise e
        finally:
            if self._status == BotStatus.RUNNING:
                self._status = BotStatus.STOPPED

    def get_status_dict(self) -> Dict[str, Any]:
        """Get detailed status information as dictionary."""
        return {
            "name": self.name,
            "status": self._status.value,
            "is_running": self.is_running,
            "uptime": self.uptime,
            "start_time": self._start_time.isoformat() if self._start_time else None,
            "last_activity": self._last_activity.isoformat() if self._last_activity else None,
            "error_count": self._error_count,
            "task_done": self._task.done() if self._task else None
        }
