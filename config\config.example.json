{"manager": {"database": {"engine": "sqlite", "database": "manager.db"}, "logging": {"level": "INFO", "file": "logs/manager.log"}, "bots": [{"name": "bot1", "mode": "test", "class": "TradingBot", "module": "services.bots.trading_bot", "params": {}, "strategy": {"module": "services.strategies.adaptive_grid_strategy", "class": "AdaptiveGridStrategy", "params": {"symbol": "FDUSDUSDC", "base_asset": "FDUSD", "quote_asset": "USDC", "grid_levels": 8, "grid_spacing": 0.0001, "total_amount": 10, "auto_adjust_bounds": true, "trend_analysis_enabled": true, "dynamic_spacing": false, "volatility_adjustment": true, "max_drawdown_percent": 5.0, "stop_loss_percent": 10.0, "position_size_percent": 95.0, "market_data_interval": 1.0, "bounds_check_interval": 300, "trend_analysis_period": 100, "price_history_limit": 1000, "use_websocket": true, "websocket_reconnect_delay": 5, "equity_check_interval": 60}}, "brokerage": {"module": "services.brokerages.binance_brokerage", "class": "BinanceBrokerage", "params": {"api_key": "your_api_key", "api_secret": "your_api_secret"}}, "database": {"engine": "sqlite"}, "logging": {"level": "INFO", "file": "logs/bot1.log"}}]}}