from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional, List, Callable
from .common import Balance, Ticker, MarketData, Order, OrderRequest, Trade

class IBrokerage(ABC):
    """Abstract base class for brokerage implementations"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_connected = False

    @abstractmethod
    async def connect(self) -> None:
        """Connect to the brokerage API"""
        pass

    @abstractmethod
    async def disconnect(self) -> None:
        """Disconnect from the brokerage API"""
        pass

    @abstractmethod
    async def get_account_balance(self) -> List[Balance]:
        """Get account balance"""
        pass

    @abstractmethod
    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker data for a symbol"""
        pass

    @abstractmethod
    async def get_market_data(self, symbol: str) -> MarketData:
        """Get market data for a symbol"""
        pass

    @abstractmethod
    async def place_order(self, order_request: OrderRequest) -> Order:
        """Place an order"""
        pass

    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        pass

    @abstractmethod
    async def get_order(self, order_id: str, symbol: str) -> Optional[Order]:
        """Get order status"""
        pass

    @abstractmethod
    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get open orders"""
        pass

    @abstractmethod
    async def get_order_history(self, symbol: str, limit: int = 100) -> List[Order]:
        """Get order history"""
        pass

    @abstractmethod
    async def get_trade_history(self, symbol: str, limit: int = 100) -> List[Trade]:
        """Get trade history"""
        pass

    @abstractmethod
    async def subscribe_to_market_data(self, symbol: str, callback: Callable[[MarketData], None]) -> None:
        """Subscribe to real-time market data"""
        pass

    @abstractmethod
    async def subscribe_to_order_updates(self, callback: Callable[[Order], None]) -> None:
        """Subscribe to order update events"""
        pass

    @abstractmethod
    async def subscribe_to_trade_updates(self, callback: Callable[[Trade], None]) -> None:
        """Subscribe to trade execution events"""
        pass

    @abstractmethod
    async def unsubscribe_from_market_data(self, symbol: str) -> None:
        """Unsubscribe from market data"""
        pass

    @abstractmethod
    async def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get symbol trading information"""
        pass

    @abstractmethod
    async def get_server_time(self) -> datetime:
        """Get server time"""
        pass