# src/manager/config_loader.py
import json
import os
from typing import Dict, Any, List
from pydantic import BaseModel, Field, field_validator
from core.exceptions import ConfigurationError


class DatabaseConfig(BaseModel):
    engine: str
    user: str = ""
    password: str = ""
    host: str = ""
    port: str = ""
    database: str = ""

    @field_validator('engine')
    def validate_engine(cls, v):
        allowed_engines = ['sqlite', 'postgres', 'mysql']
        if v not in allowed_engines:
            raise ValueError(f'Engine must be one of {allowed_engines}')
        return v


class LoggingConfig(BaseModel):
    level: str = "INFO"
    file: str = "logs/app.log"

    @field_validator('level')
    def validate_level(cls, v):
        allowed_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v not in allowed_levels:
            raise ValueError(f'Level must be one of {allowed_levels}')
        return v


class StrategyConfig(BaseModel):
    module: str
    class_name: str = Field(alias='class')
    params: Dict[str, Any] = {}


class BrokerageConfig(BaseModel):
    module: str
    class_name: str = Field(alias='class')
    params: Dict[str, Any] = {}


class BotConfig(BaseModel):
    name: str
    mode: str = "test"
    class_name: str = Field(alias='class')
    module: str
    params: Dict[str, Any] = {}
    strategy: StrategyConfig
    brokerage: BrokerageConfig
    database: DatabaseConfig
    logging: LoggingConfig

    @field_validator('mode')
    def validate_mode(cls, v):
        allowed_modes = ['test', 'live']
        if v not in allowed_modes:
            raise ValueError(f'Mode must be one of {allowed_modes}')
        return v


class ManagerConfig(BaseModel):
    database: DatabaseConfig
    logging: LoggingConfig
    bots: List[BotConfig]


class Config(BaseModel):
    manager: ManagerConfig


class ConfigLoader:
    """Configuration loader and validator"""

    @staticmethod
    def load_from_file(file_path: str) -> Config:
        """Load configuration from JSON file"""
        try:
            if not os.path.exists(file_path):
                raise ConfigurationError(
                    f"Configuration file not found: {file_path}")

            with open(file_path, 'r') as f:
                config_data = json.load(f)

            return Config(**config_data)

        except json.JSONDecodeError as e:
            raise ConfigurationError(
                f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            raise ConfigurationError(f"Error loading configuration: {e}")

    @staticmethod
    def validate_config(config: Config) -> None:
        """Validate configuration"""
        # Check for duplicate bot names
        bot_names = [bot.name for bot in config.manager.bots]
        if len(bot_names) != len(set(bot_names)):
            raise ConfigurationError(
                "Duplicate bot names found in configuration")

        # Validate log directories exist or can be created
        for bot in config.manager.bots:
            log_dir = os.path.dirname(bot.logging.file)
            if log_dir and not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir, exist_ok=True)
                except Exception as e:
                    raise ConfigurationError(
                        f"Cannot create log directory {log_dir}: {e}")

        # Validate manager log directory
        manager_log_dir = os.path.dirname(config.manager.logging.file)
        if manager_log_dir and not os.path.exists(manager_log_dir):
            try:
                os.makedirs(manager_log_dir, exist_ok=True)
            except Exception as e:
                raise ConfigurationError(
                    f"Cannot create manager log directory {manager_log_dir}: {e}")

    @staticmethod
    def create_sample_config(file_path: str) -> None:
        """Create a sample configuration file"""
        sample_config = {
            "manager": {
                "database": {
                    "engine": "sqlite",
                    "database": "manager.db"
                },
                "logging": {
                    "level": "INFO",
                    "file": "logs/manager.log"
                },
                "bots": [
                    {
                        "name": "bot1",
                        "mode": "test",
                        "class": "TradingBot",
                        "module": "services.bots.trading_bot",
                        "params": {},
                        "strategy": {
                            "module": "services.strategies.stable_coins_strategy",
                            "class": "StableCoinsStrategy",
                            "params": {}
                        },
                        "brokerage": {
                            "module": "services.brokerages.binance_brokerage",
                            "class": "BinanceBrokerage",
                            "params": {
                                "api_key": "your_api_key",
                                "api_secret": "your_api_secret"
                            }
                        },
                        "database": {
                            "engine": "sqlite"
                        },
                        "logging": {
                            "level": "INFO",
                            "file": "logs/bot1.log"
                        }
                    }
                ]
            }
        }

        with open(file_path, 'w') as f:
            json.dump(sample_config, f, indent=4)

config = ConfigLoader.load_from_file("config/config.json")
ConfigLoader.validate_config(config)
