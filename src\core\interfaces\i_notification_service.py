from abc import ABC, abstractmethod
from .common import Trade


class INotificationService(ABC):
    """Abstract base class for notification services"""

    @abstractmethod
    async def send_notification(self, message: str, level: str = "INFO") -> None:
        """Send notification"""
        pass

    @abstractmethod
    async def send_alert(self, title: str, message: str, urgent: bool = False) -> None:
        """Send alert notification"""
        pass

    @abstractmethod
    async def send_trade_notification(self, trade: Trade) -> None:
        """Send trade execution notification"""
        pass
