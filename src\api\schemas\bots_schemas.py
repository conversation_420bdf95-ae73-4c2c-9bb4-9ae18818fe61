from typing import Dict, Any, Optional
from pydantic import BaseModel


# Pydantic models for request/response schemas
class BotStatusResponse(BaseModel):
    name: str
    status: str
    uptime: Optional[float] = None
    last_activity: Optional[str] = None
    error_count: int = 0
    orders_placed: int = 0


class ConfigReloadRequest(BaseModel):
    config_path: Optional[str] = None


class BotConfigUpdate(BaseModel):
    params: Optional[Dict[str, Any]] = None
    strategy_params: Optional[Dict[str, Any]] = None
    brokerage_params: Optional[Dict[str, Any]] = None
