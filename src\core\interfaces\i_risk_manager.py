from abc import ABC, abstractmethod
from typing import Any, Dict, List
from .common import Balance, OrderRequest, OrderSide


class IRiskManager(ABC):
    """Abstract base class for risk management"""

    @abstractmethod
    async def validate_order(self, order_request: OrderRequest, account_balance: List[Balance]) -> bool:
        """Validate order against risk parameters"""
        pass

    @abstractmethod
    async def check_position_limits(self, symbol: str, quantity: float, side: OrderSide) -> bool:
        """Check if order exceeds position limits"""
        pass

    @abstractmethod
    async def calculate_position_size(self, symbol: str, price: float, risk_percent: float) -> float:
        """Calculate appropriate position size"""
        pass

    @abstractmethod
    async def should_stop_trading(self, performance_metrics: Dict[str, Any]) -> bool:
        """Determine if trading should be stopped due to risk"""
        pass
