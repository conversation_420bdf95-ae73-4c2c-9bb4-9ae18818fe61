"""
Utility helper functions for the trading bot system.
"""
import os
import json
import time
import asyncio
import importlib
from typing import Dict, Any, Optional, List, Type, Callable
from datetime import datetime, timedelta
from pathlib import Path
import logging


def ensure_directory_exists(directory_path: str) -> None:
    """Ensure a directory exists, create it if it doesn't."""
    Path(directory_path).mkdir(parents=True, exist_ok=True)


def load_json_file(file_path: str) -> Dict[str, Any]:
    """Load and parse a JSON file."""
    try:
        with open(file_path, 'r') as file:
            return json.load(file)
    except FileNotFoundError:
        raise FileNotFoundError(f"Configuration file not found: {file_path}")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in file {file_path}: {e}")


def save_json_file(data: Dict[str, Any], file_path: str) -> None:
    """Save data to a JSON file."""
    ensure_directory_exists(os.path.dirname(file_path))
    with open(file_path, 'w') as file:
        json.dump(data, file, indent=2, default=str)


def dynamic_import(module_name: str, class_name: str) -> Type:
    """
    Dynamically import a class from a module.

    Args:
        module_name: The name of the module to import from
        class_name: The name of the class to import

    Returns:
        The imported class

    Raises:
        ImportError: If the module or class cannot be imported
    """
    try:
        module = importlib.import_module(module_name)
        return getattr(module, class_name)
    except ImportError as e:
        raise ImportError(f"Cannot import module '{module_name}': {e}")
    except AttributeError as e:
        raise ImportError(
            f"Cannot find class '{class_name}' in module '{module_name}': {e}")


def validate_symbol_format(symbol: str) -> str:
    """
    Validate and normalize trading symbol format.

    Args:
        symbol: The trading symbol to validate

    Returns:
        Normalized symbol in uppercase

    Raises:
        ValueError: If symbol format is invalid
    """
    if not symbol or not isinstance(symbol, str):
        raise ValueError("Symbol must be a non-empty string")

    normalized = symbol.upper().strip()

    # Basic validation - can be extended based on requirements
    if len(normalized) < 3:
        raise ValueError(f"Invalid symbol format: {symbol}")

    return normalized


def format_price(price: float, precision: int = 8) -> str:
    """Format price with specified precision."""
    return f"{price:.{precision}f}".rstrip('0').rstrip('.')


def format_quantity(quantity: float, precision: int = 8) -> str:
    """Format quantity with specified precision."""
    return f"{quantity:.{precision}f}".rstrip('0').rstrip('.')


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values."""
    if old_value == 0:
        return 0.0
    return ((new_value - old_value) / old_value) * 100


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, returning default if division by zero."""
    if denominator == 0:
        return default
    return numerator / denominator


def get_timestamp() -> float:
    """Get current timestamp in seconds."""
    return time.time()


def get_datetime_string(dt: Optional[datetime] = None) -> str:
    """Get formatted datetime string."""
    if dt is None:
        dt = datetime.now()
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def parse_timeframe(timeframe: str) -> int:
    """
    Parse timeframe string to seconds.

    Args:
        timeframe: Timeframe string like '1m', '5m', '1h', '1d'

    Returns:
        Timeframe in seconds
    """
    timeframe = timeframe.lower()

    if timeframe.endswith('s'):
        return int(timeframe[:-1])
    elif timeframe.endswith('m'):
        return int(timeframe[:-1]) * 60
    elif timeframe.endswith('h'):
        return int(timeframe[:-1]) * 3600
    elif timeframe.endswith('d'):
        return int(timeframe[:-1]) * 86400
    else:
        raise ValueError(f"Invalid timeframe format: {timeframe}")


class CircularBuffer:
    """A circular buffer implementation for storing recent values."""

    def __init__(self, size: int):
        self.size = size
        self.buffer = []
        self.index = 0
        self.full = False

    def append(self, value: Any) -> None:
        """Add a value to the buffer."""
        if len(self.buffer) < self.size:
            self.buffer.append(value)
        else:
            self.buffer[self.index] = value
            self.full = True

        self.index = (self.index + 1) % self.size

    def get_all(self) -> List[Any]:
        """Get all values in chronological order."""
        if not self.full:
            return self.buffer.copy()

        return self.buffer[self.index:] + self.buffer[:self.index]

    def get_latest(self, count: int = 1) -> List[Any]:
        """Get the latest n values."""
        all_values = self.get_all()
        return all_values[-count:] if all_values else []

    def is_full(self) -> bool:
        """Check if buffer is full."""
        return self.full

    def size_used(self) -> int:
        """Get number of elements currently in buffer."""
        return len(self.buffer)


class RateLimiter:
    """Rate limiter for API calls."""

    def __init__(self, max_calls: int, time_window: int):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []

    async def acquire(self) -> None:
        """Acquire permission to make a call (blocks if rate limit exceeded)."""
        now = time.time()

        # Remove old calls outside the time window
        self.calls = [
            call_time for call_time in self.calls if now - call_time < self.time_window]

        # If we're at the limit, wait
        if len(self.calls) >= self.max_calls:
            sleep_time = self.time_window - (now - self.calls[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                return await self.acquire()  # Retry after sleeping

        # Record this call
        self.calls.append(now)


class RetryManager:
    """Manages retry logic with exponential backoff."""

    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay

    async def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """Execute a function with retry logic."""
        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                last_exception = e

                if attempt == self.max_retries:
                    break

                delay = min(self.base_delay * (2 ** attempt), self.max_delay)
                await asyncio.sleep(delay)

        raise last_exception


def setup_logging(name: str, level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
    """Set up a logger with console and optional file output."""
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))

    # Avoid adding handlers if they already exist
    if logger.handlers:
        return logger

    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # File handler if specified
    if log_file:
        ensure_directory_exists(os.path.dirname(log_file))
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def validate_config_section(config: Dict[str, Any], required_keys: List[str], section_name: str) -> None:
    """Validate that a configuration section has all required keys."""
    missing_keys = [key for key in required_keys if key not in config]
    if missing_keys:
        raise ValueError(
            f"Missing required keys in {section_name}: {missing_keys}")


def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
    """Merge two configuration dictionaries, with override taking precedence."""
    result = base_config.copy()

    for key, value in override_config.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = merge_configs(result[key], value)
        else:
            result[key] = value

    return result


def sanitize_filename(filename: str) -> str:
    """Sanitize a filename by removing/replacing invalid characters."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename


def calculate_moving_average(values: List[float], period: int) -> Optional[float]:
    """Calculate simple moving average for the given period."""
    if len(values) < period:
        return None

    return sum(values[-period:]) / period


def is_market_hours(timezone: str = "UTC") -> bool:
    """
    Check if current time is within market hours.
    This is a basic implementation and should be extended based on specific market requirements.
    """
    now = datetime.now()
    # Basic 24/7 crypto market assumption
    return True  # Crypto markets are always open


def format_trade_summary(order: 'Order') -> str:
    """Format a trade summary for logging."""
    return (f"{order.side.value} {order.quantity} {order.symbol} "
            f"@ {order.price} ({order.type.value}) - Status: {order.status}")


def calculate_profit_loss(entry_price: float, exit_price: float,
                          quantity: float, side: str) -> float:
    """Calculate profit/loss for a trade."""
    if side.upper() == 'BUY':
        return (exit_price - entry_price) * quantity
    else:  # SELL
        return (entry_price - exit_price) * quantity


def validate_trading_hours(symbol: str, timezone: str = "UTC") -> bool:
    """Validate if current time is within trading hours for specific symbol."""
    # Basic implementation - can be extended for specific markets
    from datetime import datetime
    import pytz

    tz = pytz.timezone(timezone)
    current_time = datetime.now(tz)

    # Crypto markets are 24/7, traditional markets have specific hours
    if symbol.endswith('USDT') or symbol.endswith('BTC'):
        return True

    # For traditional markets, implement specific logic
    return True


async def async_retry_with_backoff(func: callable, max_retries: int = 3,
                                   base_delay: float = 1.0,
                                   backoff_factor: float = 2.0) -> any:
    """Async retry with exponential backoff."""
    import asyncio

    for attempt in range(max_retries):
        try:
            return await func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e

            delay = base_delay * (backoff_factor ** attempt)
            await asyncio.sleep(delay)
