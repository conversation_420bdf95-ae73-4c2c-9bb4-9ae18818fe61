"""
Binance brokerage implementation for trading operations.
"""
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import async<PERSON>
import logging
import json
from binance import AsyncClient, BinanceSocketManager
from binance.exceptions import BinanceAPIException, BinanceOrderException

from services.brokerages.base_brokerage import BaseBrokerage
from core.interfaces import IBrokerage, OrderRequest, Order, Balance, Ticker, MarketData, OrderSide, OrderType, Trade
from core.exceptions import BrokerageError, OrderError


class BinanceBrokerage(BaseBrokerage, IBrokerage):
    """
    Binance exchange brokerage implementation using python-binance.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key')
        self.api_secret = config.get('api_secret')
        self.testnet = config.get('testnet', False)
        self._client: Optional[AsyncClient] = None
        self._socket_manager: Optional[BinanceSocketManager] = None
        self._connected = False

        # WebSocket subscriptions tracking
        self._market_data_sockets = {}
        self._user_data_socket = None
        self._market_data_callbacks = {}
        self._order_update_callbacks = []
        self._trade_update_callbacks = []

        if not self.api_key or not self.api_secret:
            raise BrokerageError(
                "API key and secret are required for Binance brokerage")

    async def connect(self) -> None:
        """Connect to Binance API."""
        try:
            await super().connect()
            self._client = await AsyncClient.create(
                api_key=self.api_key,
                api_secret=self.api_secret,
                testnet=self.testnet
            )

            # Test connection
            await self._client.ping()
            self._socket_manager = BinanceSocketManager(self._client)
            self._connected = True
            self.is_connected = True

            self.logger.info(
                f"Connected to Binance {'testnet' if self.testnet else 'mainnet'}")

        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to connect to Binance: {e}")
        except Exception as e:
            raise BrokerageError(
                f"Unexpected error connecting to Binance: {e}")

    async def disconnect(self) -> None:
        """Disconnect from Binance API."""
        try:
            # Close all WebSocket connections
            if self._user_data_socket:
                await self._user_data_socket.__aexit__(None, None, None)
                self._user_data_socket = None

            for socket in self._market_data_sockets.values():
                if socket:
                    await socket.__aexit__(None, None, None)
            self._market_data_sockets.clear()

            if self._socket_manager:
                await self._socket_manager.close()
                self._socket_manager = None

            if self._client:
                await self._client.close_connection()
                self._client = None

            self._connected = False
            self.is_connected = False
            await super().disconnect()
            self.logger.info("Disconnected from Binance")

        except Exception as e:
            self.logger.error(f"Error disconnecting from Binance: {e}")

    async def get_account_balance(self) -> List[Balance]:
        """Get account balance from Binance."""
        self._validate_connection()

        try:
            account_info = await self._client.get_account()
            balances = []

            for balance_data in account_info['balances']:
                free_amount = float(balance_data['free'])
                locked_amount = float(balance_data['locked'])

                # Only include assets with non-zero balances
                if free_amount > 0 or locked_amount > 0:
                    balances.append(Balance(
                        asset=balance_data['asset'],
                        free=free_amount,
                        locked=locked_amount,
                        total=free_amount + locked_amount
                    ))

            return balances

        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get account balance: {e}")

    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker data for a symbol."""
        self._validate_connection()

        try:
            ticker_data = await self._client.get_symbol_ticker(symbol=symbol.upper())

            return Ticker(
                symbol=symbol.upper(),
                price=float(ticker_data['price']),
                timestamp=datetime.now()
            )

        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get ticker for {symbol}: {e}")

    async def get_market_data(self, symbol: str) -> MarketData:
        """Get detailed market data for a symbol."""
        self._validate_connection()

        try:
            # Get order book ticker for bid/ask
            ticker_24hr = await self._client.get_ticker(symbol=symbol.upper())
            order_book = await self._client.get_orderbook_ticker(symbol=symbol.upper())

            return MarketData(
                symbol=symbol.upper(),
                bid=float(order_book['bidPrice']),
                ask=float(order_book['askPrice']),
                last_price=float(ticker_24hr['lastPrice']),
                volume=float(ticker_24hr['volume']),
                timestamp=datetime.now()
            )

        except BinanceAPIException as e:
            raise BrokerageError(
                f"Failed to get market data for {symbol}: {e}")

    async def place_order(self, order_request: OrderRequest) -> Order:
        """Place an order on Binance."""
        self._validate_connection()

        try:
            # Map internal order types to Binance order types
            binance_side = self._map_order_side(order_request.side)
            binance_type = self._map_order_type(order_request.order_type)

            order_params = {
                'symbol': order_request.symbol.upper(),
                'side': binance_side,
                'type': binance_type,
                'quantity': order_request.quantity,
                'timeInForce': order_request.time_in_force or 'GTC'
            }

            # Add price for limit orders
            if order_request.order_type == OrderType.LIMIT and order_request.price:
                order_params['price'] = order_request.price

            # Add stop price for stop orders
            if order_request.order_type in [OrderType.STOP_LOSS, OrderType.TAKE_PROFIT] and order_request.stop_price:
                order_params['stopPrice'] = order_request.stop_price

            # Add client order ID if provided
            if order_request.client_order_id:
                order_params['newClientOrderId'] = order_request.client_order_id

            # Place the order
            result = await self._client.create_order(**order_params)

            return self._map_binance_order_to_order(result)

        except BinanceOrderException as e:
            raise OrderError(f"Failed to place order: {e}")
        except BinanceAPIException as e:
            raise BrokerageError(f"API error placing order: {e}")

    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an existing order."""
        self._validate_connection()

        try:
            await self._client.cancel_order(
                symbol=symbol.upper(),
                orderId=int(order_id)
            )
            return True

        except BinanceAPIException as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False

    async def get_order(self, order_id: str, symbol: str) -> Optional[Order]:
        """Get order status by ID."""
        self._validate_connection()

        try:
            result = await self._client.get_order(
                symbol=symbol.upper(),
                orderId=int(order_id)
            )

            return self._map_binance_order_to_order(result)

        except BinanceAPIException as e:
            self.logger.error(f"Failed to get order {order_id}: {e}")
            return None

    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get all open orders."""
        self._validate_connection()

        try:
            params = {}
            if symbol:
                params['symbol'] = symbol.upper()

            results = await self._client.get_open_orders(**params)

            orders = []
            for order_data in results:
                orders.append(self._map_binance_order_to_order(order_data))

            return orders

        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get open orders: {e}")

    async def get_order_history(self, symbol: str, limit: int = 100) -> List[Order]:
        """Get order history for a symbol."""
        self._validate_connection()

        try:
            results = await self._client.get_all_orders(
                symbol=symbol.upper(),
                limit=min(limit, 1000)  # Binance max is 1000
            )

            orders = []
            for order_data in results:
                orders.append(self._map_binance_order_to_order(order_data))

            return orders

        except BinanceAPIException as e:
            raise BrokerageError(
                f"Failed to get order history for {symbol}: {e}")

    async def get_trade_history(self, symbol: str, limit: int = 100) -> List[Trade]:
        """Get trade history for a symbol."""
        self._validate_connection()

        try:
            results = await self._client.get_my_trades(
                symbol=symbol.upper(),
                limit=min(limit, 1000)  # Binance max is 1000
            )

            trades = []
            for trade_data in results:
                trades.append(self._map_binance_trade_to_trade(trade_data))

            return trades

        except BinanceAPIException as e:
            raise BrokerageError(
                f"Failed to get trade history for {symbol}: {e}")

    async def subscribe_to_market_data(self, symbol: str, callback: Callable[[MarketData], None]) -> None:
        """Subscribe to real-time market data via WebSocket."""
        self._validate_connection()

        try:
            symbol_upper = symbol.upper()

            # Store callback
            self._market_data_callbacks[symbol_upper] = callback

            # Create WebSocket connection for this symbol
            socket = self._socket_manager.symbol_ticker_socket(symbol_upper)
            self._market_data_sockets[symbol_upper] = socket

            # Start listening to the socket
            asyncio.create_task(self._handle_market_data_socket(
                socket, symbol_upper, callback))

            self.logger.info(f"Subscribed to market data for {symbol_upper}")

        except Exception as e:
            raise BrokerageError(
                f"Failed to subscribe to market data for {symbol}: {e}")

    async def subscribe_to_order_updates(self, callback: Callable[[Order], None]) -> None:
        """Subscribe to order update events via WebSocket."""
        self._validate_connection()

        try:
            self._order_update_callbacks.append(callback)

            # Start user data stream if not already started
            if not self._user_data_socket:
                await self._start_user_data_stream()

            self.logger.info("Subscribed to order updates")

        except Exception as e:
            raise BrokerageError(f"Failed to subscribe to order updates: {e}")

    async def subscribe_to_trade_updates(self, callback: Callable[[Trade], None]) -> None:
        """Subscribe to trade execution events via WebSocket."""
        self._validate_connection()

        try:
            self._trade_update_callbacks.append(callback)

            # Start user data stream if not already started
            if not self._user_data_socket:
                await self._start_user_data_stream()

            self.logger.info("Subscribed to trade updates")

        except Exception as e:
            raise BrokerageError(f"Failed to subscribe to trade updates: {e}")

    async def unsubscribe_from_market_data(self, symbol: str) -> None:
        """Unsubscribe from market data."""
        try:
            symbol_upper = symbol.upper()

            # Close WebSocket for this symbol
            if symbol_upper in self._market_data_sockets:
                socket = self._market_data_sockets[symbol_upper]
                if socket:
                    await socket.__aexit__(None, None, None)
                del self._market_data_sockets[symbol_upper]

            # Remove callback
            if symbol_upper in self._market_data_callbacks:
                del self._market_data_callbacks[symbol_upper]

            self.logger.info(
                f"Unsubscribed from market data for {symbol_upper}")

        except Exception as e:
            self.logger.error(
                f"Error unsubscribing from market data for {symbol}: {e}")

    async def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """Get symbol trading information."""
        self._validate_connection()

        try:
            exchange_info = await self._client.get_exchange_info()

            for symbol_info in exchange_info['symbols']:
                if symbol_info['symbol'] == symbol.upper():
                    return {
                        'symbol': symbol_info['symbol'],
                        'status': symbol_info['status'],
                        'base_asset': symbol_info['baseAsset'],
                        'quote_asset': symbol_info['quoteAsset'],
                        'base_precision': symbol_info['baseAssetPrecision'],
                        'quote_precision': symbol_info['quotePrecision'],
                        'filters': symbol_info['filters'],
                        'order_types': symbol_info['orderTypes'],
                        'is_spot_trading_allowed': symbol_info['isSpotTradingAllowed'],
                        'is_margin_trading_allowed': symbol_info['isMarginTradingAllowed']
                    }

            raise BrokerageError(f"Symbol {symbol} not found")

        except BinanceAPIException as e:
            raise BrokerageError(
                f"Failed to get symbol info for {symbol}: {e}")

    async def get_server_time(self) -> datetime:
        """Get server time."""
        self._validate_connection()

        try:
            result = await self._client.get_server_time()
            return datetime.fromtimestamp(result['serverTime'] / 1000)

        except BinanceAPIException as e:
            raise BrokerageError(f"Failed to get server time: {e}")

    async def _start_user_data_stream(self) -> None:
        """Start user data stream for order and trade updates."""
        try:
            self._user_data_socket = self._socket_manager.user_socket()
            asyncio.create_task(self._handle_user_data_socket())

        except Exception as e:
            raise BrokerageError(f"Failed to start user data stream: {e}")

    async def _handle_market_data_socket(self, socket, symbol: str, callback: Callable[[MarketData], None]) -> None:
        """Handle market data WebSocket messages."""
        try:
            async with socket as stream:
                while True:
                    try:
                        msg = await stream.recv()
                        if msg:
                            market_data = self._parse_market_data_message(
                                msg, symbol)
                            if market_data:
                                callback(market_data)
                    except Exception as e:
                        self.logger.error(
                            f"Error processing market data message: {e}")

        except Exception as e:
            self.logger.error(f"Market data socket error for {symbol}: {e}")

    async def _handle_user_data_socket(self) -> None:
        """Handle user data WebSocket messages for orders and trades."""
        try:
            async with self._user_data_socket as stream:
                while True:
                    try:
                        msg = await stream.recv()
                        if msg:
                            await self._process_user_data_message(msg)
                    except Exception as e:
                        self.logger.error(
                            f"Error processing user data message: {e}")

        except Exception as e:
            self.logger.error(f"User data socket error: {e}")

    def _parse_market_data_message(self, msg: Dict[str, Any], symbol: str) -> Optional[MarketData]:
        """Parse market data WebSocket message."""
        try:
            if msg.get('e') == '24hrTicker':
                return MarketData(
                    symbol=symbol,
                    bid=float(msg.get('b', 0)),
                    ask=float(msg.get('a', 0)),
                    last_price=float(msg.get('c', 0)),
                    volume=float(msg.get('v', 0)),
                    timestamp=datetime.now()
                )
        except Exception as e:
            self.logger.error(f"Error parsing market data message: {e}")

        return None

    async def _process_user_data_message(self, msg: Dict[str, Any]) -> None:
        """Process user data WebSocket message."""
        try:
            event_type = msg.get('e')

            if event_type == 'executionReport':
                # Order update
                order = self._parse_execution_report_to_order(msg)
                if order:
                    for callback in self._order_update_callbacks:
                        try:
                            callback(order)
                        except Exception as e:
                            self.logger.error(
                                f"Error in order update callback: {e}")

                # Trade update (if order was filled)
                if msg.get('X') == 'FILLED' or msg.get('X') == 'PARTIALLY_FILLED':
                    trade = self._parse_execution_report_to_trade(msg)
                    if trade:
                        for callback in self._trade_update_callbacks:
                            try:
                                callback(trade)
                            except Exception as e:
                                self.logger.error(
                                    f"Error in trade update callback: {e}")

        except Exception as e:
            self.logger.error(f"Error processing user data message: {e}")

    def _parse_execution_report_to_order(self, msg: Dict[str, Any]) -> Optional[Order]:
        """Parse execution report to Order object."""
        try:
            return Order(
                id=str(msg.get('i')),
                symbol=msg.get('s'),
                side=OrderSide.BUY if msg.get(
                    'S') == 'BUY' else OrderSide.SELL,
                order_type=self._map_binance_type_to_order_type(msg.get('o')),
                quantity=float(msg.get('q', 0)),
                price=float(msg.get('p', 0)) if msg.get(
                    'p') != '0.00000000' else None,
                status=msg.get('X'),
                filled_quantity=float(msg.get('z', 0)),
                created_at=datetime.fromtimestamp(
                    msg.get('O', 0) / 1000) if msg.get('O') else None,
                updated_at=datetime.fromtimestamp(
                    msg.get('T', 0) / 1000) if msg.get('T') else None
            )
        except Exception as e:
            self.logger.error(f"Error parsing execution report to order: {e}")
            return None

    def _parse_execution_report_to_trade(self, msg: Dict[str, Any]) -> Optional[Trade]:
        """Parse execution report to Trade object."""
        try:
            if float(msg.get('l', 0)) > 0:  # Only if there was a fill
                return Trade(
                    id=str(msg.get('t')),
                    order_id=str(msg.get('i')),
                    symbol=msg.get('s'),
                    side=OrderSide.BUY if msg.get(
                        'S') == 'BUY' else OrderSide.SELL,
                    quantity=float(msg.get('l', 0)),
                    price=float(msg.get('L', 0)),
                    commission=float(msg.get('n', 0)),
                    commission_asset=msg.get('N'),
                    timestamp=datetime.fromtimestamp(
                        msg.get('T', 0) / 1000) if msg.get('T') else None
                )
        except Exception as e:
            self.logger.error(f"Error parsing execution report to trade: {e}")
            return None

    def _map_order_side(self, side: OrderSide) -> str:
        """Map internal OrderSide to Binance side."""
        mapping = {
            OrderSide.BUY: 'BUY',
            OrderSide.SELL: 'SELL'
        }
        return mapping[side]

    def _map_order_type(self, order_type: OrderType) -> str:
        """Map internal OrderType to Binance order type."""
        mapping = {
            OrderType.MARKET: 'MARKET',
            OrderType.LIMIT: 'LIMIT',
            OrderType.STOP_LOSS: 'STOP_LOSS_LIMIT',
            OrderType.TAKE_PROFIT: 'TAKE_PROFIT_LIMIT'
        }
        return mapping[order_type]

    def _map_binance_order_to_order(self, binance_order: Dict[str, Any]) -> Order:
        """Map Binance order response to internal Order object."""
        return Order(
            id=str(binance_order['orderId']),
            symbol=binance_order['symbol'],
            side=OrderSide.BUY if binance_order['side'] == 'BUY' else OrderSide.SELL,
            order_type=self._map_binance_type_to_order_type(
                binance_order['type']),
            quantity=float(binance_order['origQty']),
            price=float(
                binance_order['price']) if binance_order['price'] != '0.00000000' else None,
            status=binance_order['status'],
            filled_quantity=float(binance_order['executedQty']),
            created_at=datetime.fromtimestamp(
                binance_order['time'] / 1000) if 'time' in binance_order else None,
            updated_at=datetime.fromtimestamp(
                binance_order['updateTime'] / 1000) if 'updateTime' in binance_order else None
        )

    def _map_binance_trade_to_trade(self, binance_trade: Dict[str, Any]) -> Trade:
        """Map Binance trade response to internal Trade object."""
        return Trade(
            id=str(binance_trade['id']),
            order_id=str(binance_trade['orderId']),
            symbol=binance_trade['symbol'],
            side=OrderSide.BUY if binance_trade['isBuyer'] else OrderSide.SELL,
            quantity=float(binance_trade['qty']),
            price=float(binance_trade['price']),
            commission=float(binance_trade['commission']),
            commission_asset=binance_trade['commissionAsset'],
            timestamp=datetime.fromtimestamp(binance_trade['time'] / 1000)
        )

    def _map_binance_type_to_order_type(self, binance_type: str) -> OrderType:
        """Map Binance order type to internal OrderType."""
        mapping = {
            'MARKET': OrderType.MARKET,
            'LIMIT': OrderType.LIMIT,
            'STOP_LOSS_LIMIT': OrderType.STOP_LOSS,
            'TAKE_PROFIT_LIMIT': OrderType.TAKE_PROFIT
        }
        return mapping.get(binance_type, OrderType.LIMIT)

    def _validate_connection(self) -> None:
        """Validate that the brokerage is connected."""
        if not self._connected or not self._client:
            raise BrokerageError(
                "Not connected to Binance. Call connect() first.")
