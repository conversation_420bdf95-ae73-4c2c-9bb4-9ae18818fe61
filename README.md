# Trading Bot Manager

A production-ready, scalable trading bot management system that supports multiple strategies, brokerages, and configurations running concurrently.

## Features

- **Multi-Bot Management**: Run multiple trading bots simultaneously with different configurations
- **Pluggable Architecture**: Easy-to-extend system for strategies and brokerages
- **Async/Await Support**: High-performance asynchronous operations
- **RESTful API**: FastAPI-based management interface
- **Individual Configurations**: Each bot can have its own database, logging, strategy, and brokerage settings
- **Real-time Monitoring**: Live status tracking and metrics
- **Production Ready**: Comprehensive error handling, logging, and monitoring

## Architecture Overview

The system is built with a modular architecture:

- **Manager Layer**: Orchestrates multiple bots and their lifecycles
- **Core Layer**: Abstract interfaces and base implementations
- **Services Layer**: Pluggable strategies, brokerages, databases, and logging
- **API Layer**: RESTful endpoints for management and monitoring

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd trading_manager
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Create necessary directories:
```bash
mkdir -p logs config
```

### Configuration

1. Create your configuration file at `config/config.json`:
```json
{
    "manager": {
        "database": {
            "engine": "sqlite"
        },
        "logging": {
            "level": "INFO",
            "file": "logs/manager.log"
        },
        "bots": [
            {
                "name": "bot1",
                "mode": "test",
                "class": "TradingBot",
                "module": "services.bots.trading_bot",
                "params": {},
                "strategy": {
                    "module": "services.strategies.stable_coins_strategy",
                    "class": "StableCoinsStrategy",
                    "params": {}
                },
                "brokerage": {
                    "module": "services.brokerages.binance_brokerage",
                    "class": "BinanceBrokerage",
                    "params": {
                        "api_key": "your_api_key",
                        "api_secret": "your_api_secret"
                    }
                },
                "database": {
                    "engine": "sqlite"
                },
                "logging": {
                    "level": "INFO",
                    "file": "logs/bot1.log"
                }
            }
        ]
    }
}
```

### Running

1. Start the manager:
```bash
python src/main.py
```

2. Access the API at `http://localhost:8000`

## Configuration Structure

### Manager Configuration
- `database`: Manager's database settings
- `logging`: Manager's logging configuration
- `bots`: Array of bot configurations

### Bot Configuration
- `name`: Unique bot identifier
- `mode`: "test" or "live"
- `class`: Bot class name
- `module`: Bot module path
- `params`: Bot-specific parameters
- `strategy`: Strategy configuration
- `brokerage`: Brokerage configuration
- `database`: Bot's database settings
- `logging`: Bot's logging configuration

### Database Configuration
Supports multiple database engines:

**SQLite:**
```json
{
    "engine": "sqlite",
    "database": "path/to/database.db"
}
```

**PostgreSQL:**
```json
{
    "engine": "postgres",
    "user": "username",
    "password": "password",
    "host": "localhost",
    "port": "5432",
    "database": "trading_db"
}
```

## API Endpoints

### Bot Management
- `GET /bots` - List all bots
- `GET /bots/status` - Get status of all bots
- `GET /bots/{bot_name}/status` - Get specific bot status
- `POST /bots/{bot_name}/start` - Start a bot
- `POST /bots/{bot_name}/stop` - Stop a bot
- `POST /bots/{bot_name}/restart` - Restart a bot
- `POST /bots/start-all` - Start all bots
- `POST /bots/stop-all` - Stop all bots

### Advanced Management
- `GET /bots/{bot_name}/logs` - Get bot logs
- `GET /bots/{bot_name}/metrics` - Get bot metrics
- `PUT /bots/{bot_name}/config` - Update bot configuration
- `POST /bots/{bot_name}/emergency-stop` - Emergency stop
- `GET /bots/{bot_name}/orders` - Get bot orders
- `GET /bots/{bot_name}/positions` - Get bot positions

### System Management
- `GET /health` - Health check
- `GET /system/stats` - System statistics
- `POST /system/cleanup` - System cleanup
- `POST /config/reload` - Reload configuration

### Discovery
- `GET /strategies` - List available strategies
- `GET /brokerages` - List available brokerages

## Extending the System

### Adding New Strategies

1. Create a new strategy class inheriting from `BaseStrategy`:
```python
from src.services.strategies.base_strategy import BaseStrategy
from src.core.interfaces import OrderRequest
from typing import Optional

class MyCustomStrategy(BaseStrategy):
    async def analyze_market(self, market_data) -> Optional[OrderRequest]:
        # Your strategy logic here
        pass
    
    async def on_order_filled(self, order):
        # Handle filled orders
        pass
    
    async def on_order_cancelled(self, order):
        # Handle cancelled orders
        pass
```

2. Update your configuration to use the new strategy:
```json
{
    "strategy": {
        "module": "path.to.your.strategy",
        "class": "MyCustomStrategy",
        "params": {
            "custom_param": "value"
        }
    }
}
```

### Adding New Brokerages

1. Create a new brokerage class inheriting from `BaseBrokerage`:
```python
from src.services.brokerages.base_brokerage import BaseBrokerage
from src.core.interfaces import Balance, Ticker, MarketData, Order, OrderRequest
from typing import List, Optional

class MyCustomBrokerage(BaseBrokerage):
    async def connect(self):
        # Connection logic
        pass
    
    async def get_account_balance(self) -> List[Balance]:
        # Implementation
        pass
    
    # Implement other required methods...
```

2. Update your configuration:
```json
{
    "brokerage": {
        "module": "path.to.your.brokerage",
        "class": "MyCustomBrokerage",
        "params": {
            "api_key": "your_key"
        }
    }
}
```

## Database Support

The system supports multiple database engines through SQLAlchemy:

- **SQLite**: For development and testing
- **PostgreSQL**: For production deployments
- **MySQL**: Additional support can be added

Each bot can have its own database configuration, allowing for data isolation and scaling.

## Development

### Project Structure
```
src/
├── core/           # Core interfaces and base classes
├── manager/        # Bot management and configuration
├── services/       # Pluggable services (strategies, brokerages, etc.)
├── api/           # FastAPI application and routes
└── utils/         # Utility functions and helpers
```

### Testing
Run tests with:
```bash
pytest
```

### Code Formatting
```bash
black src/
isort src/
```

### Type Checking
```bash
mypy src/
```

## Security Considerations

- Store API keys and secrets securely (use environment variables)
- Use HTTPS in production
- Implement proper authentication for the API
- Regular security audits of dependencies
- Monitor for unusual trading patterns

## Monitoring and Logging

- Structured logging with configurable levels
- Individual log files per bot
- Real-time metrics and status monitoring
- Error tracking and alerting capabilities

## Production Deployment

### Docker Deployment
Create a `Dockerfile`:
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY src/ ./src/
COPY config/ ./config/

CMD ["python", "src/main.py"]
```

### Environment Variables
```bash
export TRADING_CONFIG_PATH=/path/to/config.json
export TRADING_LOG_LEVEL=INFO
export TRADING_API_HOST=0.0.0.0
export TRADING_API_PORT=8000
```

### Monitoring
- Use tools like Prometheus and Grafana for monitoring
- Set up alerts for bot failures or unusual behavior
- Regular backup of configuration and trading data

## Support

For issues and questions:
1. Check the documentation
2. Review the example configurations
3. Enable debug logging for troubleshooting
4. Check the API endpoints for system status

## License

[Your License Here]