"""
Additional API routes for the trading bot management system.
"""

from typing import Any, Dict, List
from manager import bot_manager
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from manager.bot_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from manager.config_loader import config
import logging
from api.dependencies import get_bot_manager

# Router instance
router = APIRouter()

# Routes


@router.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Trading Bot Manager API", "version": "1.0.0"}


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "bots_count": len(bot_manager.bots) if bot_manager else 0
    }


@router.get("/strategies", response_model=List[Dict[str, Any]])
async def list_available_strategies(
    bot_manager: bot_manager.BotManager = Depends(get_bot_manager)
) -> List[Dict[str, Any]]:
    """List all available strategy classes."""
    try:
        strategies = await bot_manager.list_available_strategies()
        return strategies
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/brokerages", response_model=List[Dict[str, Any]])
async def list_available_brokerages(
    bot_manager: bot_manager.BotManager = Depends(get_bot_manager)
) -> List[Dict[str, Any]]:
    """List all available brokerage classes."""
    try:
        brokerages = await bot_manager.list_available_brokerages()
        return brokerages
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config/reload")
async def reload_config(background_tasks: BackgroundTasks, bot_manager: BotManager = Depends(get_bot_manager)):
    """Reload configuration"""
    def reload_task():
        try:
            return bot_manager.reload_config(config)
        except Exception as e:
            logging.error(f"Failed to reload config: {e}")
            raise

    background_tasks.add_task(reload_task)
    return {"message": "Reloading configuration"}


@router.get("/system/stats", response_model=Dict[str, Any])
async def get_system_stats(
    bot_manager: BotManager = Depends(get_bot_manager)
) -> Dict[str, Any]:
    """Get overall system statistics."""
    try:
        stats = await bot_manager.get_system_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/system/cleanup")
async def cleanup_system(
    background_tasks: BackgroundTasks,
    bot_manager: BotManager = Depends(get_bot_manager)
) -> Dict[str, str]:
    """Perform system cleanup operations."""
    try:
        background_tasks.add_task(bot_manager.cleanup_system)
        return {"message": "System cleanup initiated"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
